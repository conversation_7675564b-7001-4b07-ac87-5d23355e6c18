import AdmZip from 'adm-zip';
import <PERSON> from 'papa<PERSON><PERSON>';
import { insertAggTrades, findAggTrades } from '../database/aggTrade';
import { fetchAggTrades } from '../exchange/binance';
import { format } from '../pipeline/aggTrade/stages';
import { TPipelineData } from '../types/pipeline.types';

/**
 * Downloads and unzips historical aggregate trade data from Binance data vision
 */
async function downloadAndUnzip(symbol: string, date: string): Promise<boolean> {
    try {
        const url = `https://data.binance.vision/data/spot/daily/aggTrades/${symbol}/${symbol}-aggTrades-${date}.zip`;
        const outputFileName = __dirname + `/${symbol}-aggTrades-${date}.csv`;

        console.log(`Downloading ${url}...`);
        const response = await fetch(url);
        
        if (!response.ok) {
            console.warn(`Failed to download ${symbol} for ${date}: ${response.statusText}`);
            return false;
        }

        const arrayBuffer = await response.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        const zip = new AdmZip(buffer);
        const zipEntries = zip.getEntries();

        if (zipEntries.length === 0) {
            throw new Error('No entries found in the zip file.');
        }

        const csvEntry = zipEntries.find(entry => entry.entryName.endsWith('.csv'));
        if (!csvEntry) {
            throw new Error('No CSV file found inside the zip archive.');
        }

        const csvBuffer = csvEntry.getData();
        const file = Bun.file(outputFileName);
        await file.write(csvBuffer);

        console.log(`File downloaded and unzipped successfully to ${outputFileName}!`);
        return true;
    } catch (error) {
        console.error(`Error downloading ${symbol} for ${date}:`, error.message);
        return false;
    }
}

/**
 * Converts CSV string to object array compatible with database schema
 */
function csvToObjectArray(csvString: string) {
    const results = Papa.parse(csvString, {
        header: false,
        dynamicTyping: true,
        skipEmptyLines: true
    });

    if (results.errors.length > 0) {
        console.error('CSV Parsing Errors:', results.errors);
    }

    const mappedData = results.data.map(row => {
        if (row.length < 8) {
            console.warn("Skipping row due to insufficient columns:", row);
            return null;
        }

        return {
            aggId: row[0],
            price: row[1],
            qty: row[2],
            time: row[5] / 1000,
            isMaker: row[6] === 'True' ? 1 : 0
        };
    }).filter(Boolean);

    return mappedData;
}

/**
 * Processes downloaded CSV file and inserts data into database
 */
async function processHistoricalData(symbol: string, date: string): Promise<number> {
    const fileName = `${(symbol + 'usdt').toUpperCase()}-aggTrades-${date}.csv`;
    const file = Bun.file(__dirname + `/${fileName}`);
    
    if (!await file.exists()) {
        console.warn(`File ${fileName} does not exist`);
        return 0;
    }

    const buf = await file.arrayBuffer();
    const csvString = new TextDecoder().decode(buf);
    const list = csvToObjectArray(csvString);
    const result = insertAggTrades(symbol, list);
    
    console.log(`${symbol} ${date}: ${result.inserted} trades inserted`);
    return result.inserted;
}

/**
 * Fetches recent aggregate trades via API to fill gap between historical data and yesterday
 */
async function fetchRecentTrades(symbol: string): Promise<number> {
    try {
        // Find the latest trade in database
        const latestTrades = await findAggTrades({ symbol, limit: 1, endTime: Date.now() });
        
        if (latestTrades.length === 0) {
            console.log(`No existing trades found for ${symbol}, skipping API fetch`);
            return 0;
        }

        const latestId = latestTrades[0].aggId;
        let totalInserted = 0;

        console.log(`Fetching recent trades for ${symbol} starting from aggId: ${latestId}`);

        await fetchAggTrades({
            symbol,
            startId: latestId,
            onData: (data) => {
                const list: any[] = [];
                for (const d of data) {
                    const formatted = format({
                        type: 'api',
                        extra: { symbol },
                        data: d
                    }) as TPipelineData;
                    
                    if (formatted === null) continue;
                    list.push(formatted.data);
                }
                
                if (list.length > 0) {
                    const result = insertAggTrades(symbol, list);
                    totalInserted += result.inserted;
                    console.log(`${symbol}: ${result.inserted} recent trades inserted`);
                }
            }
        });

        return totalInserted;
    } catch (error) {
        console.error(`Error fetching recent trades for ${symbol}:`, error.message);
        return 0;
    }
}

/**
 * Gets yesterday's date in YYYY-MM-DD format
 */
function getYesterday(): string {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return yesterday.toISOString().slice(0, 10);
}

/**
 * Generates array of dates from startDate to yesterday
 */
function getDateRange(startDate: string): string[] {
    const dates: string[] = [];
    const start = new Date(startDate);
    const yesterday = new Date(getYesterday());
    
    const current = new Date(start);
    while (current <= yesterday) {
        dates.push(current.toISOString().slice(0, 10));
        current.setDate(current.getDate() + 1);
    }
    
    return dates;
}

/**
 * Main function to initialize trade data for a new crypto symbol
 */
export async function initializeCrypto(symbol: string, startDate?: string) {
    console.log(`\n=== Initializing trade data for ${symbol.toUpperCase()} ===`);
    
    // Default to 30 days ago if no start date provided
    const defaultStartDate = new Date();
    defaultStartDate.setDate(defaultStartDate.getDate() - 30);
    const start = startDate || defaultStartDate.toISOString().slice(0, 10);
    
    console.log(`Start date: ${start}`);
    console.log(`End date: ${getYesterday()} (yesterday)`);
    
    const dates = getDateRange(start);
    console.log(`Processing ${dates.length} days of historical data...`);
    
    let totalDownloaded = 0;
    let totalInserted = 0;
    
    // Step 1: Download and process historical data
    for (const date of dates) {
        const symbolWithUsdt = (symbol + 'usdt').toUpperCase();
        const downloaded = await downloadAndUnzip(symbolWithUsdt, date);
        
        if (downloaded) {
            totalDownloaded++;
            const inserted = await processHistoricalData(symbol, date);
            totalInserted += inserted;
            
            // Small delay to be respectful to Binance servers
            await Bun.sleep(100);
        }
    }
    
    console.log(`\nHistorical data summary:`);
    console.log(`- Files downloaded: ${totalDownloaded}/${dates.length}`);
    console.log(`- Total trades inserted: ${totalInserted}`);
    
    // Step 2: Fetch recent trades via API to fill gap to yesterday
    console.log(`\nFetching recent trades via API...`);
    const recentInserted = await fetchRecentTrades(symbol);
    
    console.log(`\n=== Initialization complete for ${symbol.toUpperCase()} ===`);
    console.log(`Total historical trades: ${totalInserted}`);
    console.log(`Total recent trades: ${recentInserted}`);
    console.log(`Grand total: ${totalInserted + recentInserted} trades`);
}

// Example usage (uncomment to run):
// await initializeCrypto('hbar', '2025-08-01');
