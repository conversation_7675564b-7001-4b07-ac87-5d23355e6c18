import { initializeCrypto } from './initializeCrypto';

/**
 * Example script to initialize trade data for new crypto symbols
 * 
 * Usage examples:
 * 1. Initialize with default 30 days of history:
 *    await initializeCrypto('sol')
 * 
 * 2. Initialize with custom start date:
 *    await initializeCrypto('sol', '2025-08-01')
 * 
 * 3. Initialize multiple symbols:
 *    for (const symbol of ['sol', 'ada', 'dot']) {
 *        await initializeCrypto(symbol, '2025-08-01')
 *    }
 */

async function main() {
    try {
        // Example: Initialize HBAR with data from August 1st, 2025 to yesterday
        await initializeCrypto('hbar', '2025-08-01');
        
        // Example: Initialize SOL with default 30 days of history
        // await initializeCrypto('sol');
        
        // Example: Initialize multiple symbols
        // const symbolsToInitialize = ['ada', 'dot', 'matic'];
        // for (const symbol of symbolsToInitialize) {
        //     console.log(`\n--- Processing ${symbol} ---`);
        //     await initializeCrypto(symbol, '2025-08-01');
        //     // Add delay between symbols to be respectful to Binance
        //     await Bun.sleep(2000);
        // }
        
    } catch (error) {
        console.error('Error during initialization:', error);
        process.exit(1);
    }
}

// Run the script
await main();
