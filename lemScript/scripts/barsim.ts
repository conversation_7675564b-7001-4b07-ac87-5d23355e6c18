//@version=5
indicator("Similar Size Opposite Color Bars Signal", shorttitle="SSOC Signal", overlay=false)

// Input parameters
size_tolerance = input.float(20.0, "Size Tolerance (%)", minval=1.0, maxval=50.0, 
                            tooltip="Maximum percentage difference between bar sizes")
min_body_ratio = input.float(30.0, "Minimum Body Ratio (%)", minval=10.0, maxval=90.0,
                            tooltip="Minimum body size as percentage of total bar range")
max_wick_ratio = input.float(60.0, "Maximum Wick Ratio (%)", minval=10.0, maxval=90.0,
                            tooltip="Maximum combined wick size as percentage of total bar range")
atr_multiplier = input.float(0.3, "ATR Multiplier for Min Size", minval=0.1, maxval=2.0,
                            tooltip="Minimum bar size as multiple of ATR to filter tiny candles")

// Display options
signal_color_bull = input.color(color.green, "Bullish Signal Color")
signal_color_bear = input.color(color.red, "Bearish Signal Color")
show_histogram = input.bool(true, "Show Histogram")
show_line = input.bool(false, "Show Line Plot")

// Calculate ATR for dynamic sizing
atr_length = 14
current_atr = ta.atr(atr_length)

// Function to calculate bar metrics for any bar
get_bar_metrics(bar_index) =>
    bar_open = open[bar_index]
    bar_high = high[bar_index]
    bar_low = low[bar_index]
    bar_close = close[bar_index]
    
    // Basic measurements
    total_range = bar_high - bar_low
    body_size = math.abs(bar_close - bar_open)
    upper_wick = bar_high - math.max(bar_open, bar_close)
    lower_wick = math.min(bar_open, bar_close) - bar_low
    total_wick = upper_wick + lower_wick
    
    // Ratios
    body_ratio = total_range > 0 ? (body_size / total_range) * 100 : 0
    wick_ratio = total_range > 0 ? (total_wick / total_range) * 100 : 0
    
    // Bar direction
    is_bullish = bar_close > bar_open
    
    [total_range, body_size, body_ratio, wick_ratio, is_bullish]

// Main pattern detection function
detect_pattern() =>
    // Skip if we don't have enough bars
    if bar_index < 1
        [0, false]
    else
        // Get metrics for current and previous bars
        [curr_range, curr_body, curr_body_ratio, curr_wick_ratio, curr_bullish] = get_bar_metrics(0)
        [prev_range, prev_body, prev_body_ratio, prev_wick_ratio, prev_bullish] = get_bar_metrics(1)
        
        // Filter conditions
        min_size_threshold = current_atr * atr_multiplier
        
        // Check if bars meet minimum size requirement
        curr_size_ok = curr_range >= min_size_threshold
        prev_size_ok = prev_range >= min_size_threshold
        
        // Check body and wick ratios
        curr_body_ok = curr_body_ratio >= min_body_ratio
        prev_body_ok = prev_body_ratio >= min_body_ratio
        curr_wick_ok = curr_wick_ratio <= max_wick_ratio
        prev_wick_ok = prev_wick_ratio <= max_wick_ratio
        
        // Check if colors are opposite
        opposite_colors = curr_bullish != prev_bullish
        
        // Calculate size similarity
        size_diff_pct = prev_range > 0 ? math.abs(curr_range - prev_range) / prev_range * 100 : 100
        similar_size = size_diff_pct <= size_tolerance
        
        // Combined pattern detection
        pattern_detected = curr_size_ok and prev_size_ok and 
                          curr_body_ok and prev_body_ok and 
                          curr_wick_ok and prev_wick_ok and 
                          opposite_colors and similar_size
        
        // Return signal and direction
        signal = pattern_detected ? 1 : 0
        current_is_bullish = curr_bullish
        
        [signal, current_is_bullish]

// Get the signal and direction
[pattern_signal, is_current_bullish] = detect_pattern()

// Create different signal values for bullish/bearish
signal_value = pattern_signal == 1 ? (is_current_bullish ? 1 : -1) : 0

// Plot the main signal
plot(show_line ? pattern_signal : na, title="Signal Line", color=color.blue, linewidth=2)

// Plot histogram with colors
plot(show_histogram ? signal_value : na, title="Signal Histogram", 
     style=plot.style_histogram, linewidth=3,
     color=signal_value > 0 ? signal_color_bull : signal_value < 0 ? signal_color_bear : color.gray)

// Plot zero line
hline(0, "Zero Line", color=color.gray, linestyle=hline.style_dashed)

// Add horizontal lines for reference
hline(1, "Signal Level", color=color.green, linestyle=hline.style_dotted, alpha=50)
hline(-1, "Signal Level", color=color.red, linestyle=hline.style_dotted, alpha=50)

// Output the binary signal for external use
plot(pattern_signal, title="Binary Signal (0/1)", display=display.data_window, color=color.yellow)
